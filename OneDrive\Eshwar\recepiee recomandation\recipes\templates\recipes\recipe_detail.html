{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        {% if recipe.image %}
        <div class="mb-4">
            <img src="{{ recipe.image.url }}" alt="{{ recipe.title }}" class="img-fluid rounded" style="max-height: 400px; width: 100%; object-fit: cover;">
        </div>
        {% endif %}

        <h1>{{ recipe.title }}</h1>
        <p class="text-muted">
            Category: {{ recipe.category.name }} |
            Difficulty: {{ recipe.difficulty|title }} |
            Cooking Time: {{ recipe.cooking_time }} minutes |
            Servings: {{ recipe.servings }}
        </p>

        <!-- Recipe Stats -->
        <div class="d-flex align-items-center mb-3">
            <span class="me-3">
                <i class="fas fa-eye text-muted"></i> {{ recipe.views_count }} views
            </span>
            <span class="me-3">
                <i class="fas fa-heart text-danger"></i> {{ total_likes }} likes
            </span>
            <span class="me-3">
                <i class="fas fa-star text-warning"></i>
                {% if avg_rating %}
                    {{ avg_rating|floatformat:1 }}/5 ({{ total_reviews }} reviews)
                {% else %}
                    No ratings yet
                {% endif %}
            </span>
            {% if user.is_staff %}
            <span class="me-3">
                <a href="{% url 'analytics_dashboard' %}" class="btn btn-sm btn-outline-info">
                    <i class="fas fa-chart-bar"></i> Analytics
                </a>
            </span>
            {% endif %}
        </div>

        <h3>Description</h3>
        <p>{{ recipe.description }}</p>

        <h3>Ingredients</h3>
        <div class="ingredients">
            {{ recipe.ingredients|linebreaks }}
        </div>

        <h3>Instructions</h3>
        <div class="instructions">
            {{ recipe.instructions|linebreaks }}
        </div>

        {% if user.is_authenticated %}
        <div class="mt-4">
            <a href="{% url 'toggle_favorite' recipe.pk %}" class="btn btn-outline-danger">
                {% if user_has_liked %}Unlike{% else %}Like{% endif %}
            </a>
        </div>
        {% endif %}
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Recipe Info</h5>
            </div>
            <div class="card-body">
                <p><strong>Category:</strong> {{ recipe.category.name }}</p>
                <p><strong>Difficulty:</strong> {{ recipe.difficulty|title }}</p>
                <p><strong>Cooking Time:</strong> {{ recipe.cooking_time }} minutes</p>
                <p><strong>Servings:</strong> {{ recipe.servings }}</p>
                <p><strong>Created:</strong> {{ recipe.created_at|date:"M d, Y" }}</p>
                {% if recipe.author %}
                <p><strong>Author:</strong> {{ recipe.author.username }}</p>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5>Reviews</h5>
            </div>
            <div class="card-body">
                {% for review in recipe.reviews.all %}
                <div class="mb-3">
                    <strong>{{ review.user.username }}</strong>
                    <span class="text-warning">
                        {% for i in "12345" %}
                            {% if forloop.counter <= review.rating %}★{% else %}☆{% endif %}
                        {% endfor %}
                    </span>
                    <p class="mb-1">{{ review.comment }}</p>
                    <small class="text-muted">{{ review.created_at|date:"M d, Y" }}</small>
                </div>
                {% empty %}
                <p>No reviews yet.</p>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
