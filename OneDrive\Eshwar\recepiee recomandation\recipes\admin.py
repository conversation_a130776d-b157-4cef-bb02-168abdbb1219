from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
    from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
    from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
    from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
    from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment');
from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')from django.contrib import admin
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')
from .models import Recipe, Category, Rating

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created
