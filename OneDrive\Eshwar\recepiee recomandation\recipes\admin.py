from django.contrib import admin
from .models import Recipe, Category, Rating, Review, Like, UserProfile

@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    list_display = ('title', 'cooking_time', 'servings', 'difficulty', 'category', 'created_at')
    list_filter = ('difficulty', 'category', 'created_at')
    search_fields = ('title', 'description', 'ingredients')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'score', 'created_at')
    list_filter = ('score', 'created_at')
    search_fields = ('recipe__title', 'comment')

@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'user', 'rating', 'created_at')
    list_filter = ('rating', 'created_at')
    search_fields = ('recipe__title', 'user__username', 'comment')

@admin.register(Like)
class LikeAdmin(admin.ModelAdmin):
    list_display = ('recipe', 'user', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('recipe__title', 'user__username')

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'get_favorite_count')
    search_fields = ('user__username',)
    
    def get_favorite_count(self, obj):
        return obj.favorite_recipes.count()
    get_favorite_count.short_description = 'Favorite Recipes Count'
