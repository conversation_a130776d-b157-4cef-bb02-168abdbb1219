from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.db.models import Q
from .models import Recipe, Review, Like
from .forms import RecipeForm, ReviewForm
from django.shortcuts import render
from .models import Recipe
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from .models import Recipe, Category, Rating
from .forms import RecipeForm, ReviewForm
from django.urls import reverse_lazy

class HomeView(ListView):
    model = Recipe
    template_name = 'recipes/home.html'
    context_object_name = 'recipes'
    ordering = ['-created_at']
    paginate_by = 6

class RecipeListView(ListView):
    model = Recipe
    template_name = 'recipes/recipe_list.html'
    context_object_name = 'recipes'
    paginate_by = 10

class RecipeDetailView(DetailView):
    model = Recipe
    template_name = 'recipes/recipe_detail.html'

class RecipeCreateView(CreateView):
    model = Recipe
    form_class = RecipeForm
    template_name = 'recipes/recipe_form.html'
    success_url = reverse_lazy('recipe_list')

@login_required
def recipe_create(request):
    if request.method == 'POST':
        form = RecipeForm(request.POST, request.FILES)
        if form.is_valid():
            recipe = form.save(commit=False)
            recipe.author = request.user
            recipe.save()
            return redirect('recipe_detail', pk=recipe.pk)
    else:
        form = RecipeForm()
    return render(request, 'recipes/recipe_form.html', {'form': form})

def recipe_detail(request, pk):
    recipe = get_object_or_404(Recipe, pk=pk)
    recipe.views_count += 1
    recipe.save()
    
    if request.user.is_authenticated:
        user_review = Review.objects.filter(recipe=recipe, user=request.user).first()
        user_has_liked = Like.objects.filter(recipe=recipe, user=request.user).exists()
    else:
        user_review = None
        user_has_liked = False
    
    return render(request, 'recipes/recipe_detail.html', {
        'recipe': recipe,
        'user_review': user_review,
        'user_has_liked': user_has_liked
    })

@login_required
def toggle_favorite(request, pk):
    recipe = get_object_or_404(Recipe, pk=pk)
    profile = request.user.userprofile
    
    if recipe in profile.favorite_recipes.all():
        profile.favorite_recipes.remove(recipe)
    else:
        profile.favorite_recipes.add(recipe)
    
    return redirect('recipe_detail', pk=pk)
