<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Recipe Recommender{% endblock %}</title>
    <meta name="description" content="{% block description %}Discover and share amazing recipes with our intelligent recipe recommendation system{% endblock %}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #4facfe;
            --warning-color: #ffeaa7;
            --danger-color: #fd79a8;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .navbar-nav .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 0 0.25rem;
            padding: 0.5rem 1rem !important;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .search-form {
            position: relative;
        }

        .search-form input {
            border-radius: 25px;
            border: 2px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.1);
            color: white;
            padding: 0.75rem 1.25rem;
            backdrop-filter: blur(10px);
        }

        .search-form input::placeholder {
            color: rgba(255,255,255,0.8);
        }

        .search-form input:focus {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
            color: white;
        }

        .btn-search {
            border-radius: 50%;
            width: 45px;
            height: 45px;
            border: 2px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.1);
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn-search:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            transform: scale(1.1);
            color: white;
        }

        .dropdown-menu {
            border: none;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }

        .dropdown-item {
            padding: 0.75rem 1.25rem;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 0.25rem;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            transform: translateX(5px);
        }

        .alert {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .main-content {
            background: rgba(255,255,255,0.9);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 2rem 0;
            overflow: hidden;
        }

        .footer {
            background: linear-gradient(135deg, var(--dark-color) 0%, #636e72 100%);
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
        }

        .loading-spinner {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
        }

        @media (max-width: 768px) {
            .navbar-nav {
                text-align: center;
                margin-top: 1rem;
            }

            .search-form {
                margin: 1rem 0;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="fas fa-utensils"></i> Recipe Recommender
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'recipe_list' %}">
                            <i class="fas fa-list"></i> All Recipes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'featured_recipes' %}">
                            <i class="fas fa-star"></i> Featured
                        </a>
                    </li>
                    {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'recipe_create' %}">
                            <i class="fas fa-plus"></i> Add Recipe
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <!-- Search Form -->
                <form class="d-flex me-3 search-form" method="GET" action="{% url 'search_recipes' %}">
                    <input class="form-control me-2" type="search" name="q" placeholder="Search recipes..." aria-label="Search" value="{{ request.GET.q }}">
                    <button class="btn btn-search" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>

                <!-- User Menu -->
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'user_profile' %}">
                                <i class="fas fa-user-circle"></i> My Profile
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'user_analytics' %}">
                                <i class="fas fa-chart-line"></i> My Analytics
                            </a></li>
                            {% if user.is_staff %}
                            <li><a class="dropdown-item" href="{% url 'analytics_dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> Admin Dashboard
                            </a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'logout' %}">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'login' %}">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'register' %}">
                            <i class="fas fa-user-plus"></i> Register
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Loading Spinner -->
    <div class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Messages -->
        {% if messages %}
        <div class="row">
            <div class="col-12">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %}"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        {% block content %}
        {% endblock %}
    </div>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5><i class="fas fa-utensils"></i> Recipe Recommender</h5>
                    <p class="text-light">Discover amazing recipes, share your culinary creations, and get personalized recommendations powered by AI.</p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>Recipes</h6>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'recipe_list' %}" class="text-light text-decoration-none">All Recipes</a></li>
                        <li><a href="{% url 'search_recipes' %}?category=1" class="text-light text-decoration-none">Appetizers</a></li>
                        <li><a href="{% url 'search_recipes' %}?category=2" class="text-light text-decoration-none">Main Courses</a></li>
                        <li><a href="{% url 'search_recipes' %}?category=3" class="text-light text-decoration-none">Desserts</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>Features</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light text-decoration-none">Recipe Search</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Meal Planning</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Shopping Lists</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Nutrition Info</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>Community</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light text-decoration-none">Recipe Reviews</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Cooking Tips</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Food Blog</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Forums</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>Support</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light text-decoration-none">Help Center</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Contact Us</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Privacy Policy</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4" style="border-color: rgba(255,255,255,0.2);">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-light">&copy; 2024 Recipe Recommender. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-light">Made with <i class="fas fa-heart text-danger"></i> for food lovers</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Enhanced search functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchForm = document.querySelector('.search-form');
            const searchInput = searchForm.querySelector('input[name="q"]');

            // Search suggestions (you can populate this from backend)
            const searchSuggestions = [
                'pasta', 'chicken', 'vegetarian', 'dessert', 'healthy',
                'quick meals', 'italian', 'indian', 'mexican', 'salad'
            ];

            // Auto-complete functionality
            searchInput.addEventListener('input', function() {
                const value = this.value.toLowerCase();
                if (value.length > 2) {
                    const suggestions = searchSuggestions.filter(s => s.includes(value));
                    // You can implement dropdown suggestions here
                }
            });

            // Loading spinner for form submissions
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    showLoadingSpinner();
                });
            });

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });

        function showLoadingSpinner() {
            document.querySelector('.loading-spinner').style.display = 'block';
        }

        function hideLoadingSpinner() {
            document.querySelector('.loading-spinner').style.display = 'none';
        }

        // Hide loading spinner when page loads
        window.addEventListener('load', hideLoadingSpinner);

        // Enhanced dropdown animations
        document.querySelectorAll('.dropdown-toggle').forEach(dropdown => {
            dropdown.addEventListener('click', function() {
                const menu = this.nextElementSibling;
                if (menu) {
                    menu.style.transform = 'translateY(-10px)';
                    menu.style.opacity = '0';
                    setTimeout(() => {
                        menu.style.transform = 'translateY(0)';
                        menu.style.opacity = '1';
                    }, 10);
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>