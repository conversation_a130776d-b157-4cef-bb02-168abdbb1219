from django.db.models import Count
from .models import Recipe, UserProfile

def get_recommendations(user, limit=10):
    if not user.is_authenticated:
        return Recipe.objects.order_by('-views_count')[:limit]
    
    profile = user.userprofile
    user_preferences = profile.dietary_preferences
    
    # Get recipes based on user's dietary preferences
    recommended = Recipe.objects.filter(
        cuisine_type__in=user_preferences.get('preferred_cuisines', [])
    )
    
    # Include popular recipes
    popular_recipes = Recipe.objects.annotate(
        total_score=Count('likes') + Count('reviews')
    ).order_by('-total_score')
    
    # Combine and remove duplicates
    recommended = (list(recommended) + list(popular_recipes))[:limit]
    
    return recommended