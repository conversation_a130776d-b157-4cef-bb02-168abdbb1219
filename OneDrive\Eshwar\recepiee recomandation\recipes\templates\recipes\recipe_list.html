{% extends 'base.html' %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1><i class="fas fa-list"></i> All Recipes</h1>
        <p class="text-muted">Discover amazing recipes from our community</p>
    </div>
    <div class="col-md-4 text-end">
        {% if user.is_authenticated %}
        <a href="{% url 'recipe_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Recipe
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Recipes</label>
                <input type="text" class="form-control" id="search" name="q"
                       value="{{ current_query }}" placeholder="Search by title, description, or ingredients...">
            </div>
            <div class="col-md-2">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category" name="category">
                    <option value="">All Categories</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}" {% if current_category == category.id|stringformat:"s" %}selected{% endif %}>
                        {{ category.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="difficulty" class="form-label">Difficulty</label>
                <select class="form-select" id="difficulty" name="difficulty">
                    <option value="">All Levels</option>
                    {% for value, label in difficulties %}
                    <option value="{{ value }}" {% if current_difficulty == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="max_time" class="form-label">Max Time (min)</label>
                <input type="number" class="form-control" id="max_time" name="max_time"
                       value="{{ current_max_time }}" placeholder="e.g. 30">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Results Count -->
<div class="row mb-3">
    <div class="col-12">
        <p class="text-muted">
            {% if current_query or current_category or current_difficulty or current_max_time %}
                Showing {{ recipes|length }} recipe{{ recipes|length|pluralize }}
                {% if current_query %}for "{{ current_query }}"{% endif %}
                <a href="{% url 'recipe_list' %}" class="btn btn-sm btn-outline-secondary ms-2">
                    <i class="fas fa-times"></i> Clear Filters
                </a>
            {% else %}
                Showing all {{ recipes|length }} recipe{{ recipes|length|pluralize }}
            {% endif %}
        </p>
    </div>
</div>

<!-- Recipes Grid -->
<div class="row">
    {% for recipe in recipes %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 recipe-card">
            <div class="card-body">
                <h5 class="card-title">{{ recipe.title }}</h5>
                <p class="card-text">{{ recipe.description|truncatewords:20 }}</p>

                <div class="recipe-meta mb-3">
                    <small class="text-muted">
                        <span class="badge bg-secondary me-1">{{ recipe.category.name }}</span>
                        <span class="badge bg-info me-1">{{ recipe.difficulty|title }}</span>
                    </small>
                    <br>
                    <small class="text-muted mt-1 d-block">
                        <i class="fas fa-clock"></i> {{ recipe.cooking_time }} min
                        <i class="fas fa-users ms-2"></i> {{ recipe.servings }} servings
                        <i class="fas fa-eye ms-2"></i> {{ recipe.views_count }} views
                    </small>
                </div>

                <!-- Recipe Stats -->
                <div class="recipe-stats mb-2">
                    <small class="text-muted">
                        {% if recipe.avg_rating %}
                        <span class="text-warning">
                            {% for i in "12345" %}
                                {% if forloop.counter <= recipe.avg_rating|floatformat:0 %}★{% else %}☆{% endif %}
                            {% endfor %}
                        </span>
                        ({{ recipe.avg_rating|floatformat:1 }})
                        {% endif %}

                        {% if recipe.like_count %}
                        <i class="fas fa-heart text-danger ms-2"></i> {{ recipe.like_count }}
                        {% endif %}
                    </small>
                </div>
            </div>

            <div class="card-footer bg-transparent">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="{% url 'recipe_detail' recipe.pk %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye"></i> View Recipe
                    </a>
                    {% if recipe.author %}
                    <small class="text-muted">by {{ recipe.author.username }}</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h4>No recipes found</h4>
            <p class="text-muted">Try adjusting your search criteria or browse all recipes.</p>
            <a href="{% url 'recipe_list' %}" class="btn btn-primary">View All Recipes</a>
        </div>
    </div>
    {% endfor %}
</div>

<style>
.recipe-card {
    transition: transform 0.2s, box-shadow 0.2s;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.recipe-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.recipe-meta {
    font-size: 0.85rem;
}

.recipe-stats {
    font-size: 0.8rem;
}
</style>
{% endblock %}