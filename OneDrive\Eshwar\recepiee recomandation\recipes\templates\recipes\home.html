{% extends 'base.html' %}

{% block content %}
<h1 class="mb-4">Welcome to Recipe Recommender</h1>

<div class="row">
    {% for recipe in recipes %}
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">{{ recipe.title }}</h5>
                <p class="card-text">{{ recipe.description|truncatewords:30 }}</p>
                <a href="{% url 'recipe_detail' recipe.pk %}" class="btn btn-primary">View Recipe</a>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<div class="pagination">
    <span class="step-links">
        {% if page_obj.has_previous %}
            <a href="?page=1">&laquo; first</a>
            <a href="?page={{ page_obj.previous_page_number }}">previous</a>
        {% endif %}

        <span class="current">
            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
        </span>

        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}">next</a>
            <a href="?page={{ page_obj.paginator.num_pages }}">last &raquo;</a>
        {% endif %}
    </span>
</div>
{% endblock %}